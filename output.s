	.text
	.def	@feat.00;
	.scl	3;
	.type	0;
	.endef
	.globl	@feat.00
.set @feat.00, 0
	.file	"main"
	.def	main;
	.scl	2;
	.type	32;
	.endef
	.globl	__real@40200000                 # -- Begin function main
	.section	.rdata,"dr",discard,__real@40200000
	.p2align	2, 0x0
__real@40200000:
	.long	0x40200000                      # float 2.5
	.text
	.globl	main
	.p2align	4, 0x90
main:                                   # @main
.seh_proc main
# %bb.0:                                # %entry
	subq	$40, %rsp
	.seh_stackalloc 40
	.seh_endprologue
	leaq	.Lformat_str(%rip), %rcx
	movd	__real@40200000(%rip), %xmm2    # xmm2 = [2.5E+0,0.0E+0,0.0E+0,0.0E+0]
	movl	$1, %edx
	movq	%xmm2, %r8
	callq	printf
	xorl	%eax, %eax
	addq	$40, %rsp
	retq
	.seh_endproc
                                        # -- End function
	.section	.rdata,"dr"
.Lformat_str:                           # @format_str
	.asciz	"%d %f\n"

	.globl	_fltused
